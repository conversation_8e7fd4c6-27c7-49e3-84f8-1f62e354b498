---
type: "always_apply"
---

## 规则1：语言使用规范
- 必须使用中文进行所有回复和交流
- 所有解释、说明、文档均使用中文表达
- 确保语言表达准确、清晰

## 规则2：代码分析规范
- 使用MCP工具提升代码分析效率
- 确保对代码功能有准确的理解

## 规则3：代码修改验证规范
- 每次代码修改完成后必须立即执行编译验证
- 如果编译出现错误，必须继续修复直至所有错误解决
- 不允许留下任何编译错误
- 确保修改后的代码能够成功编译运行

## 规则4：功能完整性保护规范
- 修复代码错误时禁止删除或简化原有功能
- 不允许通过移除代码来"解决"编译错误
- 必须在保持原有功能完整性的前提下进行错误修复
- 确保修复后的代码功能不少于修复前

## 规则5：信息获取准确性规范
- 遇到不确定的内容或代码结构时，必须使用工具查看相关文件
- 禁止基于猜测、假设或经验进行回答
- 禁止编造或虚构不存在的信息
- 所有回答和修改必须基于实际的文件内容和代码结构

## 规则6：文档生成限制规范
- 禁止在任务完成后主动生成总结性markdown文件
- 仅在用户明确要求时才创建文档文件
- 避免产生不必要的文件，保持工作空间整洁
- 专注于完成用户的具体技术需求

## 规则7：Enhance prompt功能的语言要求
-当使用Enhance prompt功能时，增强之后的提示词，必须用中文显示

## 规则8：程序自动启动的限制
-每次执行完成任务后，不要自动启动程序

## 规则9：测试程序文件的规则
- 禁止生成测试程序，除非用户额外要求
- 如果需要创建测试程序，创建的测试程序文件必须放置在根目录文件下的“Tests”文件夹下

## 规则10：文本要求
- 创建文本和回答时，必须使用通俗易懂的直白语言
- 不要生成不必要的多余内容，只针对必要的内容进行文本生成

## 规则11：Serena工具使用规范
- **强制优先级**：所有代码分析、编辑、查找操作必须首选Serena工具
- **符号级操作**：禁止使用简单文本替换，必须使用符号级精确操作
- **项目理解优先**：编辑前必须使用`get_symbols_overview`了解代码结构
- **验证驱动**：每次编辑后必须使用Serena验证功能确保代码正确性
- **内存管理**：合理使用Serena的内存功能记录项目关键信息

## 规则12：Serena工作流程规范
- **分析阶段**：使用`find_symbol`和`get_symbols_overview`深入理解代码
- **编辑阶段**：使用`replace_symbol_body`、`insert_after_symbol`等符号级工具
- **验证阶段**：使用Serena内置验证确保编辑正确性
- **记录阶段**：使用`write_memory`记录重要发现和决策

##通用规则要求
- 严格遵循核心工作流程，使用中文与专业程序员交互，保持简洁专业的沟通风格

## AI模型要求
- **基础模型**：Claude 4.0 (Claude Sonnet 4)
- **开发商**：Anthropic
- **版本要求**：必须使用Claude 4.0或更高版本
- **能力要求**：支持代码生成、分析、调试和优化功能

## 工作模式定义
- Augment Code的工作模式分为6种，分别对应不同的工作阶段和任务类型
- 每种模式下，AI助手的响应内容和行为都有严格的规定
- 必须严格按照模式要求进行工作，不得擅自越界

### [模式：研究] - 需求分析阶段
- 使用`codebase-retrieval`工具深入理解现有代码结构
- 使用`context7-mcp`查询相关技术文档和最佳实践
- 使用`deepwiki-mcp`快速获取背景知识和技术原理
- 使用`sequential-thinking`分析复杂需求的技术可行性
- 分析用户需求的技术可行性和影响范围
- 识别相关的文件、类、方法和数据库表

### [模式：构思] - 方案设计阶段
- 使用`sequential-thinking`进行复杂方案的深度思考和设计
- 使用`context7-mcp`获取最新的技术方案和示例代码
- 使用`deepwiki-mcp`获取成熟设计范式与领域通识
- 提供可行的技术方案
- 方案包含：实现思路、技术栈、优缺点分析、工作量评估
- 格式：`[简要描述] - 优点：[...] 缺点：[...] 工作量：[...]`

### [模式：计划] - 详细规划阶段
- 使用`sequential-thinking`制定复杂项目的详细执行计划
- 使用`mcp-shrimp-task-manager`拆解任务并管理依赖关系
- 将选定方案分解为具体的执行步骤
- 每个步骤包含：操作的具体文件路径、涉及的类/方法/属性名称、修改的代码行数范围、预期的功能结果、依赖的外部库
- 创建任务文档：`./issues/[任务名称].md`

### [模式：执行] - 代码实现阶段
- 严格按照计划顺序执行每个步骤
- 使用`str-replace-editor`工具进行代码修改（每次不超过500行）
- 使用`desktop-commander`进行文件系统操作和命令执行
- 使用`mcp-shrimp-task-manager`跟踪任务执行状态与依赖关系
- 使用`sequential-thinking`分析和解决复杂的技术问题
- 遇到问题时请全面的分析，定位到原因后修复

### [模式：评审] - 质量检查阶段
- 对照原计划检查所有功能是否正确实现
- 使用`desktop-commander`运行编译测试，确保无语法错误
- 使用`sequential-thinking`进行全面的质量分析
- 总结完成的工作和遗留问题
- 使用`mcp-feedback-enhanced`请求用户最终确认

### [模式：快速] - 紧急响应模式
- 跳过完整工作流程，直接处理简单问题
- 适用于：bug修复、小幅调整、配置更改
- 可根据需要使用任何相关工具快速解决问题

## 开发工作流程
- **代码检索**：优先使用`serena`工具进行精确的符号级代码分析，辅助使用`codebase-retrieval`
- **代码编辑**：优先使用`serena`的符号级编辑工具，必要时使用`str-replace-editor`
- **项目理解**：使用`serena`的`get_symbols_overview`和`find_symbol`深入理解代码结构
- **文件操作**：使用`desktop-commander`进行系统级文件操作和命令执行
- **复杂分析**：使用`sequential-thinking`进行深度问题分析和方案设计
- **技术查询**：使用`context7-mcp`获取最新的技术文档和示例
- **知识背景补充**：使用`deepwiki-mcp`补充架构知识和行业术语
- **任务管理**：使用`mcp-shrimp-task-manager`进行任务拆分与状态追踪
- **自检验证**：在提交文件或解决方案前，必须先进行自检以确保其功能正常
- **分步执行**：大型文件处理应采用分步执行策略，确保操作不会因文件大小而中断

## MCP服务优先级
1. `serena` - 专业代码分析、编辑和项目管理（最高优先级）
2. `mcp-feedback-enhanced` - 用户交互和确认
3. `sequential-thinking` - 复杂问题分析和深度思考
4. `context7-mcp` - 查询最新库文档和示例
5. `deepwiki-mcp` - 获取背景知识和领域概念
6. `mcp-shrimp-task-manager` - 拆分与管理任务依赖
7. `codebase-retrieval` - 分析现有代码结构
8. `desktop-commander` - 系统文件操作和命令执行

## 工具使用指南

### Serena - 专业代码分析与编辑工具
- **用途**：专业级代码分析、精确编辑、项目管理和符号操作
- **适用场景**：所有代码相关操作的首选工具
- **核心优势**：
  - 基于LSP的精确符号定位和操作
  - 支持复杂的代码重构和结构化编辑
  - 提供项目级的代码理解和导航
  - 内置编译验证和错误检测
- **使用原则**：
  - **优先使用**：任何涉及代码分析、编辑、查找的操作都应首选Serena
  - **精确操作**：利用符号级操作而非简单的文本替换
  - **结构化编辑**：使用`find_symbol`、`replace_symbol_body`等符号级工具
  - **项目理解**：使用`get_symbols_overview`获取代码结构概览
  - **验证优先**：每次编辑后使用Serena的验证功能确保代码正确性

### Sequential Thinking
- **用途**：复杂问题的逐步分析
- **适用场景**：需求分析、方案设计、问题排查
- **使用时机**：遇到复杂逻辑或多步骤问题时

### Context 7
- **用途**：查询最新的技术文档、API参考和代码示例
- **适用场景**：技术调研、最佳实践获取
- **使用时机**：需要了解新技术或验证实现方案时

### DeepWiki MCP
- **用途**：检索背景知识、行业术语、常见架构和设计模式
- **适用场景**：研究、构思阶段需要理解技术原理和通识
- **使用时机**：遇到术语不清、原理未知、需引入通用范式时

### MCP Shrimp Task Manager
- **用途**：任务拆解、依赖管理、任务进度跟踪
- **适用场景**：详细计划阶段与执行阶段
- **使用时机**：任务过多需管理依赖、跟踪状态、建立任务树时

### Desktop Commander
- **用途**：执行系统命令、文件操作、运行测试
- **适用场景**：项目管理、测试执行、文件处理
- **使用时机**：需要进行系统级操作时

## 工作流程控制
- **强制反馈**：每个阶段完成后必须使用`mcp-feedback-enhanced`
- **任务结束**：持续调用`mcp-feedback-enhanced`直到用户反馈为空
- **代码复用**：优先使用现有代码结构，避免重复开发
- **文件位置**：所有项目文件必须在项目目录内部
- **工具协同**：根据任务复杂度合理组合使用多个MCP工具

## 执行原则
每次响应必须以当前模式标签开始，严格按照工作流程推进，确保代码质量和项目一致性。