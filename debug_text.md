[InsertSelectionBranchAfterStep] 选中元素: 初始步
[选择分支] 首次插入，现有分支: 0个
[选择分支] 新分支位置: 221,319.5
[选择分支] 创建: Initial, 位置: 221,319.5
[分支集合] 添加分支，位置: 221,319.5
[SFCBranchViewModel] 初始化适配器: BranchType=Selection, ElementType=SelectionBranch
[SFCBranchViewModel] ✅ 选择分支适配器配置完成，数量: 4
[SFCBranchViewModel] 🔧 索引2适配器类型: Output (修复为Output)
[分支集合] ViewModel位置: 221,319.5
[SFCCanvas] 为新创建的分支添加位置变化监听: 4cfca0a8-673f-4643-af89-e92ea8d7c781
[AddConnection] 开始执行: c33969a2-3779-418e-9dbf-4d2849998d2d -> 4cfca0a8-673f-4643-af89-e92ea8d7c781
[AddConnection] 对象查找结果: sourceObject=SFCStepModel, targetObject=SFCBranchModel
[AddConnection] 位置获取: 源ViewModel位置=200,200, 源Model位置=200,200
[AddConnection] 位置获取: 目标ViewModel位置=221,319.5, 目标Model位置=221,319.5
[GetPreciseConnectPointPosition] ⚠️ 回退到计算方法
[GetPreciseConnectPointPosition] ⚠️ 回退到计算方法
[CalculateElementConnectPoint] SFCBranchModel: 类型=Selection, 位置=221,319.5, 输出点=False
[对比调试] 如果是步骤输入点，位置应该是: 271,325.5
[CalculateElementConnectPoint] ✅ 选择分支左上连接点(索引0): 242,326.5
[AddConnection] 创建连接: c33969a2-3779-418e-9dbf-4d2849998d2d -> 4cfca0a8-673f-4643-af89-e92ea8d7c781
[AddConnection] 源位置: 200,200, 目标位置: 221,319.5
[AddConnection] 源对象类型: SFCStepModel, 目标对象类型: SFCBranchModel
[AddConnection] 源连接点: 250,322, 目标连接点: 242,326.5
[AddConnection] 连接创建完成，路径点数量: 2
[AddConnectionViewModel] 连接 87df6aea-0523-4032-9bf6-79b2c25c15bf 路径点数量: 2
[AddConnectionViewModel] ViewModel路径点数量: 2
[AddConnectionViewModel] 起点: 250,322, 终点: 242,326.5
[AddConnection] 🔄 开始更新连接点状态: c33969a2-3779-418e-9dbf-4d2849998d2d -> 4cfca0a8-673f-4643-af89-e92ea8d7c781
[UpdateElementConnectPointState] 🔍 开始更新连接点状态:
  元素类型: SFCStepViewModel
  连接点索引: 0
  是否输入: False
  连接ID: 87df6aea-0523-4032-9bf6-79b2c25c15bf
[UpdateElementConnectPointState] ✅ 连接点状态更新成功: SFCStepViewModel, Direction=Output, Index=0, AdapterIndex=0, ConnectionId: 87df6aea-0523-4032-9bf6-79b2c25c15bf
[UpdateElementConnectPointState] 🔍 开始更新连接点状态:
  元素类型: SFCBranchViewModel
  连接点索引: 0
  是否输入: True
  连接ID: 87df6aea-0523-4032-9bf6-79b2c25c15bf
[UpdateElementConnectPointState] 🎯 找到分支元素适配器: 4cfca0a8-673f-4643-af89-e92ea8d7c781, 分支类型: Selection, 适配器数量: 4
[UpdateElementConnectPointState] ✅ 连接点状态更新成功: SFCBranchViewModel, Direction=Input, Index=0, AdapterIndex=0, ConnectionId: 87df6aea-0523-4032-9bf6-79b2c25c15bf
[UpdateConnectPointStates] 连接点状态更新完成: c33969a2-3779-418e-9dbf-4d2849998d2d[0] -> 4cfca0a8-673f-4643-af89-e92ea8d7c781[0]
[选择分支] 跳过垂直对齐，保持计算位置: 221,319.5
[选择分支] 左分支使用竖直连接线，无需创建转换条件
[选择分支] 右分支转换条件已内嵌在选择分支视图中，无需单独创建
System.Windows.Data Error: 40 : BindingExpression path error: 'Description' property not found on 'object' ''SFCBranchViewModel' (HashCode=31021623)'. BindingExpression:Path=SelectedElement.Description; DataItem='EnhancedSFCViewModel' (HashCode=34232944); target element is 'TextBlock' (Name=''); target property is 'Text' (type 'String')
[InitializeConnectPoints] 开始初始化连接点，ViewModel.Id: 4cfca0a8-673f-4643-af89-e92ea8d7c781
[InitializeConnectPoints] XAML连接点查找结果: LeftTop=True, LeftBottom=True, RightTop=True, RightBottom=True
[SFCSelectionBranchView] 开始设置连接点ElementId: 4cfca0a8-673f-4643-af89-e92ea8d7c781
[SFCSelectionBranchView] ✅ LeftTop连接点ElementId设置: 4cfca0a8-673f-4643-af89-e92ea8d7c781
[SFCSelectionBranchView] ✅ LeftBottom连接点ElementId设置: 4cfca0a8-673f-4643-af89-e92ea8d7c781
[SFCSelectionBranchView] ✅ RightTop连接点ElementId设置: 4cfca0a8-673f-4643-af89-e92ea8d7c781
[SFCSelectionBranchView] ✅ RightBottom连接点ElementId设置: 4cfca0a8-673f-4643-af89-e92ea8d7c781
[InitializeConnectPoints] LeftTop连接点ElementId: '4cfca0a8-673f-4643-af89-e92ea8d7c781', 期望: '4cfca0a8-673f-4643-af89-e92ea8d7c781'
[InitializeConnectPoints] ✅ 已修复LeftTop连接点ElementId: '4cfca0a8-673f-4643-af89-e92ea8d7c781'
[SFCSelectionBranchView] 适配器检查: Count=4, 期望>=4
[SFCSelectionBranchView] ✅ LeftTop适配器绑定: Direction=Input, Index=0
[SFCSelectionBranchView] ✅ LeftBottom适配器绑定: Direction=Output, Index=1
[SFCSelectionBranchView] ✅ RightTop适配器绑定: Direction=Output, Index=2
[SFCSelectionBranchView] ✅ RightBottom适配器绑定: Direction=Output, Index=3
[SFCSelectionBranchView] 开始同步现有连接状态: 4cfca0a8-673f-4643-af89-e92ea8d7c781
[SFCSelectionBranchView] 找到 1 个相关连接
[SFCSelectionBranchView] ✅ 同步输入连接: 87df6aea-0523-4032-9bf6-79b2c25c15bf, 索引: 0
System.Windows.Data Error: 40 : BindingExpression path error: 'IsInitialStep' property not found on 'object' ''SFCBranchViewModel' (HashCode=31021623)'. BindingExpression:Path=IsInitialStep; DataItem='SFCBranchViewModel' (HashCode=31021623); target element is 'SFCConnectPoint' (Name='LeftTopConnectPoint'); target property is 'NoTarget' (type 'Object')
System.Windows.Data Error: 40 : BindingExpression path error: 'IsInitialStep' property not found on 'object' ''SFCBranchViewModel' (HashCode=31021623)'. BindingExpression:Path=IsInitialStep; DataItem='SFCBranchViewModel' (HashCode=31021623); target element is 'SFCConnectPoint' (Name='LeftBottomConnectPoint'); target property is 'NoTarget' (type 'Object')
System.Windows.Data Error: 40 : BindingExpression path error: 'IsInitialStep' property not found on 'object' ''SFCBranchViewModel' (HashCode=31021623)'. BindingExpression:Path=IsInitialStep; DataItem='SFCBranchViewModel' (HashCode=31021623); target element is 'SFCConnectPoint' (Name='TopConnectPoint1'); target property is 'NoTarget' (type 'Object')
System.Windows.Data Error: 40 : BindingExpression path error: 'IsInitialStep' property not found on 'object' ''SFCBranchViewModel' (HashCode=31021623)'. BindingExpression:Path=IsInitialStep; DataItem='SFCBranchViewModel' (HashCode=31021623); target element is 'SFCConnectPoint' (Name='TopConnectPoint2'); target property is 'NoTarget' (type 'Object')
System.Windows.Data Error: 40 : BindingExpression path error: 'IsInitialStep' property not found on 'object' ''SFCBranchViewModel' (HashCode=31021623)'. BindingExpression:Path=IsInitialStep; DataItem='SFCBranchViewModel' (HashCode=31021623); target element is 'SFCConnectPoint' (Name='LeftTopConnectPoint'); target property is 'NoTarget' (type 'Object')
System.Windows.Data Error: 40 : BindingExpression path error: 'IsInitialStep' property not found on 'object' ''SFCBranchViewModel' (HashCode=31021623)'. BindingExpression:Path=IsInitialStep; DataItem='SFCBranchViewModel' (HashCode=31021623); target element is 'SFCConnectPoint' (Name='LeftParallelPoint'); target property is 'NoTarget' (type 'Object')
System.Windows.Data Error: 40 : BindingExpression path error: 'IsInitialStep' property not found on 'object' ''SFCBranchViewModel' (HashCode=31021623)'. BindingExpression:Path=IsInitialStep; DataItem='SFCBranchViewModel' (HashCode=31021623); target element is 'SFCConnectPoint' (Name='RightParallelPoint'); target property is 'NoTarget' (type 'Object')
[InitializeConnectPoints] 连接点已初始化，跳过重复初始化: 4cfca0a8-673f-4643-af89-e92ea8d7c781
System.Windows.Data Error: 4 : Cannot find source for binding with reference 'RelativeSource FindAncestor, AncestorType='System.Windows.Controls.UserControl', AncestorLevel='1''. BindingExpression:Path=DataContext.InsertSelectionBranchFromAnyPartCommand; DataItem=null; target element is 'MenuItem' (Name=''); target property is 'Command' (type 'ICommand')
System.Windows.Data Error: 4 : Cannot find source for binding with reference 'RelativeSource FindAncestor, AncestorType='System.Windows.Controls.UserControl', AncestorLevel='1''. BindingExpression:Path=DataContext.InsertSiblingBranchCommand; DataItem=null; target element is 'MenuItem' (Name=''); target property is 'Command' (type 'ICommand')
System.Windows.Data Error: 4 : Cannot find source for binding with reference 'RelativeSource FindAncestor, AncestorType='System.Windows.Controls.UserControl', AncestorLevel='1''. BindingExpression:Path=DataContext.InsertParallelBranchFromAnyPartCommand; DataItem=null; target element is 'MenuItem' (Name=''); target property is 'Command' (type 'ICommand')
[连接点重叠检测] 点1: (250.0, 322.0), 点2: (242.0, 326.5), 距离: 9.2px, 重叠: True
[CreateConnectionPath] 连接 87df6aea-0523-4032-9bf6-79b2c25c15bf 连接点重叠，隐藏连接线
[CreateConnectionPath] 起点: (250.0, 322.0), 终点: (242.0, 326.5)
连接 87df6aea-0523-4032-9bf6-79b2c25c15bf 连接点重叠，使用连接点重叠方式
[AddConnection] 延迟更新连接线 87df6aea-0523-4032-9bf6-79b2c25c15bf 的路径点
[CalculateElementConnectPoint] ✅ 选择分支ViewModel左上连接点(索引0): 250,326.5
[AddConnection] 延迟更新 - 源对象类型: SFCStepViewModel, 目标对象类型: SFCBranchViewModel
[AddConnection] 延迟更新 - 源位置: 200,200, 目标位置: 221,319.5
[AddConnection] 延迟更新后的路径点: 250,322 -> 250,326.5
[AddConnection] PathPoints集合已更新，应该触发重新评估
[SFCCanvas] 位置变化: SFCBranchViewModel.IsSelected
[SFCCanvas] 位置变化: SFCBranchViewModel.SelectedPart
[SFCSelectionBranchView] 右侧区域选中并开始拖拽: 4cfca0a8-673f-4643-af89-e92ea8d7c781
[SFCCanvas] 位置变化: SFCBranchViewModel.IsSelected
System.Windows.Data Error: 40 : BindingExpression path error: 'Description' property not found on 'object' ''SFCBranchViewModel' (HashCode=31021623)'. BindingExpression:Path=SelectedElement.Description; DataItem='EnhancedSFCViewModel' (HashCode=34232944); target element is 'TextBlock' (Name=''); target property is 'Text' (type 'String')
[InsertSiblingBranch] 横线端点连接位置计算:
  当前分支位置: X=221, Y=319.5
  当前分支ViewType: Initial
  前一个分支横线右端: 361
  连接间隙: 0
  计算公式: 361 + 0 - 40 - (-46)
  新分支位置: X=367, Y=319.5
  验证：新分支横线最左端 = 361
  常量值: RIGHT_PART_LEFT=40, LINE_LEFT_OFFSET=-46, LINE_RIGHT_OFFSET=100, GAP=0
[SFCBranchViewModel] 初始化适配器: BranchType=Selection, ElementType=SelectionBranch
[SFCBranchViewModel] ✅ 选择分支适配器配置完成，数量: 4
[SFCBranchViewModel] 🔧 索引2适配器类型: Output (修复为Output)
[SFCCanvas] 位置变化: SFCBranchViewModel.NextBranchId
[SFCCanvas] 为新创建的分支添加位置变化监听: 1d60c884-941e-495c-9967-efae044cc95f
[CreateBranchChainConnection] 选择分支扩展：使用左侧下端连接点（索引1）
[CreateBranchChainConnection] 分支链连接: Selection 源索引2 -> Selection 目标索引1
[CreateBranchChainConnection] 创建分支链连接: 4cfca0a8-673f-4643-af89-e92ea8d7c781 -> 1d60c884-941e-495c-9967-efae044cc95f
[AddConnection] 开始执行: 4cfca0a8-673f-4643-af89-e92ea8d7c781 -> 1d60c884-941e-495c-9967-efae044cc95f
[AddConnection] 对象查找结果: sourceObject=SFCBranchModel, targetObject=SFCBranchModel
[AddConnection] 位置获取: 源ViewModel位置=221,319.5, 源Model位置=221,319.5
[AddConnection] 位置获取: 目标ViewModel位置=367,319.5, 目标Model位置=367,319.5
[GetPreciseConnectPointPosition] ⚠️ 回退到计算方法
[CalculateElementConnectPoint] SFCBranchModel: 类型=Selection, 位置=221,319.5, 输出点=True
[CalculateElementConnectPoint] ✅ 选择分支右上连接点(索引2): 388,354.5
[GetPreciseConnectPointPosition] ⚠️ 回退到计算方法
[CalculateElementConnectPoint] SFCBranchModel: 类型=Selection, 位置=367,319.5, 输出点=False
[对比调试] 如果是步骤输入点，位置应该是: 417,325.5
[CalculateElementConnectPoint] ✅ 选择分支左下连接点(索引1): 396,354.5
[AddConnection] 创建连接: 4cfca0a8-673f-4643-af89-e92ea8d7c781 -> 1d60c884-941e-495c-9967-efae044cc95f
[AddConnection] 源位置: 221,319.5, 目标位置: 367,319.5
[AddConnection] 源对象类型: SFCBranchModel, 目标对象类型: SFCBranchModel
[AddConnection] 源连接点: 388,354.5, 目标连接点: 396,354.5
[AddConnection] 连接创建完成，路径点数量: 2
[AddConnectionViewModel] 连接 f6073675-39eb-4016-af64-77a3b8616672 路径点数量: 2
[AddConnectionViewModel] ViewModel路径点数量: 2
[AddConnectionViewModel] 起点: 388,354.5, 终点: 396,354.5
[AddConnection] 🔄 开始更新连接点状态: 4cfca0a8-673f-4643-af89-e92ea8d7c781 -> 1d60c884-941e-495c-9967-efae044cc95f
[UpdateElementConnectPointState] 🔍 开始更新连接点状态:
  元素类型: SFCBranchViewModel
  连接点索引: 2
  是否输入: False
  连接ID: f6073675-39eb-4016-af64-77a3b8616672
[UpdateElementConnectPointState] 🎯 找到分支元素适配器: 4cfca0a8-673f-4643-af89-e92ea8d7c781, 分支类型: Selection, 适配器数量: 4
[UpdateElementConnectPointState] ✅ 连接点状态更新成功: SFCBranchViewModel, Direction=Output, Index=2, AdapterIndex=2, ConnectionId: f6073675-39eb-4016-af64-77a3b8616672
[UpdateElementConnectPointState] 🔍 开始更新连接点状态:
  元素类型: SFCBranchViewModel
  连接点索引: 1
  是否输入: True
  连接ID: f6073675-39eb-4016-af64-77a3b8616672
[UpdateElementConnectPointState] 🎯 找到分支元素适配器: 1d60c884-941e-495c-9967-efae044cc95f, 分支类型: Selection, 适配器数量: 4
[UpdateElementConnectPointState] ⚠️ 精确匹配失败，使用Direction匹配: Direction=Input, Index=1
[UpdateElementConnectPointState] ✅ 连接点状态更新成功: SFCBranchViewModel, Direction=Input, Index=1, AdapterIndex=0, ConnectionId: f6073675-39eb-4016-af64-77a3b8616672
[UpdateConnectPointStates] 连接点状态更新完成: 4cfca0a8-673f-4643-af89-e92ea8d7c781[2] -> 1d60c884-941e-495c-9967-efae044cc95f[1]
[ValidateAndAdjustBranchChainLayout] 调整分支链布局，共2个分支
[ValidateAndAdjustBranchChainLayout] 分支链布局调整完成
System.Windows.Data Error: 40 : BindingExpression path error: 'Description' property not found on 'object' ''SFCBranchViewModel' (HashCode=35768611)'. BindingExpression:Path=SelectedElement.Description; DataItem='EnhancedSFCViewModel' (HashCode=34232944); target element is 'TextBlock' (Name=''); target property is 'Text' (type 'String')
[InitializeConnectPoints] 开始初始化连接点，ViewModel.Id: 1d60c884-941e-495c-9967-efae044cc95f
[InitializeConnectPoints] XAML连接点查找结果: LeftTop=True, LeftBottom=True, RightTop=True, RightBottom=True
[SFCSelectionBranchView] 开始设置连接点ElementId: 1d60c884-941e-495c-9967-efae044cc95f
[SFCSelectionBranchView] ✅ LeftTop连接点ElementId设置: 1d60c884-941e-495c-9967-efae044cc95f
[SFCSelectionBranchView] ✅ LeftBottom连接点ElementId设置: 1d60c884-941e-495c-9967-efae044cc95f
[SFCSelectionBranchView] ✅ RightTop连接点ElementId设置: 1d60c884-941e-495c-9967-efae044cc95f
[SFCSelectionBranchView] ✅ RightBottom连接点ElementId设置: 1d60c884-941e-495c-9967-efae044cc95f
[InitializeConnectPoints] LeftTop连接点ElementId: '1d60c884-941e-495c-9967-efae044cc95f', 期望: '1d60c884-941e-495c-9967-efae044cc95f'
[InitializeConnectPoints] ✅ 已修复LeftTop连接点ElementId: '1d60c884-941e-495c-9967-efae044cc95f'
[SFCSelectionBranchView] 适配器检查: Count=4, 期望>=4
[SFCSelectionBranchView] ✅ LeftTop适配器绑定: Direction=Input, Index=0
[SFCSelectionBranchView] ✅ LeftBottom适配器绑定: Direction=Output, Index=1
[SFCSelectionBranchView] ✅ RightTop适配器绑定: Direction=Output, Index=2
[SFCSelectionBranchView] ✅ RightBottom适配器绑定: Direction=Output, Index=3
[SFCSelectionBranchView] 开始同步现有连接状态: 1d60c884-941e-495c-9967-efae044cc95f
[SFCSelectionBranchView] 找到 1 个相关连接
System.Windows.Data Error: 40 : BindingExpression path error: 'IsInitialStep' property not found on 'object' ''SFCBranchViewModel' (HashCode=35768611)'. BindingExpression:Path=IsInitialStep; DataItem='SFCBranchViewModel' (HashCode=35768611); target element is 'SFCConnectPoint' (Name='LeftTopConnectPoint'); target property is 'NoTarget' (type 'Object')
System.Windows.Data Error: 40 : BindingExpression path error: 'IsInitialStep' property not found on 'object' ''SFCBranchViewModel' (HashCode=35768611)'. BindingExpression:Path=IsInitialStep; DataItem='SFCBranchViewModel' (HashCode=35768611); target element is 'SFCConnectPoint' (Name='LeftBottomConnectPoint'); target property is 'NoTarget' (type 'Object')
System.Windows.Data Error: 40 : BindingExpression path error: 'IsInitialStep' property not found on 'object' ''SFCBranchViewModel' (HashCode=35768611)'. BindingExpression:Path=IsInitialStep; DataItem='SFCBranchViewModel' (HashCode=35768611); target element is 'SFCConnectPoint' (Name='TopConnectPoint1'); target property is 'NoTarget' (type 'Object')
System.Windows.Data Error: 40 : BindingExpression path error: 'IsInitialStep' property not found on 'object' ''SFCBranchViewModel' (HashCode=35768611)'. BindingExpression:Path=IsInitialStep; DataItem='SFCBranchViewModel' (HashCode=35768611); target element is 'SFCConnectPoint' (Name='TopConnectPoint2'); target property is 'NoTarget' (type 'Object')
System.Windows.Data Error: 40 : BindingExpression path error: 'IsInitialStep' property not found on 'object' ''SFCBranchViewModel' (HashCode=35768611)'. BindingExpression:Path=IsInitialStep; DataItem='SFCBranchViewModel' (HashCode=35768611); target element is 'SFCConnectPoint' (Name='LeftTopConnectPoint'); target property is 'NoTarget' (type 'Object')
System.Windows.Data Error: 40 : BindingExpression path error: 'IsInitialStep' property not found on 'object' ''SFCBranchViewModel' (HashCode=35768611)'. BindingExpression:Path=IsInitialStep; DataItem='SFCBranchViewModel' (HashCode=35768611); target element is 'SFCConnectPoint' (Name='LeftParallelPoint'); target property is 'NoTarget' (type 'Object')
System.Windows.Data Error: 40 : BindingExpression path error: 'IsInitialStep' property not found on 'object' ''SFCBranchViewModel' (HashCode=35768611)'. BindingExpression:Path=IsInitialStep; DataItem='SFCBranchViewModel' (HashCode=35768611); target element is 'SFCConnectPoint' (Name='RightParallelPoint'); target property is 'NoTarget' (type 'Object')
[InitializeConnectPoints] 连接点已初始化，跳过重复初始化: 1d60c884-941e-495c-9967-efae044cc95f
System.Windows.Data Error: 4 : Cannot find source for binding with reference 'RelativeSource FindAncestor, AncestorType='System.Windows.Controls.UserControl', AncestorLevel='1''. BindingExpression:Path=DataContext.InsertSelectionBranchFromAnyPartCommand; DataItem=null; target element is 'MenuItem' (Name=''); target property is 'Command' (type 'ICommand')
System.Windows.Data Error: 4 : Cannot find source for binding with reference 'RelativeSource FindAncestor, AncestorType='System.Windows.Controls.UserControl', AncestorLevel='1''. BindingExpression:Path=DataContext.InsertSiblingBranchCommand; DataItem=null; target element is 'MenuItem' (Name=''); target property is 'Command' (type 'ICommand')
System.Windows.Data Error: 4 : Cannot find source for binding with reference 'RelativeSource FindAncestor, AncestorType='System.Windows.Controls.UserControl', AncestorLevel='1''. BindingExpression:Path=DataContext.InsertParallelBranchFromAnyPartCommand; DataItem=null; target element is 'MenuItem' (Name=''); target property is 'Command' (type 'ICommand')
[连接点重叠检测] 点1: (388.0, 354.5), 点2: (396.0, 354.5), 距离: 8.0px, 重叠: True
[CreateConnectionPath] 连接 f6073675-39eb-4016-af64-77a3b8616672 连接点重叠，隐藏连接线
[CreateConnectionPath] 起点: (388.0, 354.5), 终点: (396.0, 354.5)
连接 f6073675-39eb-4016-af64-77a3b8616672 连接点重叠，使用连接点重叠方式
[AddConnection] 延迟更新连接线 f6073675-39eb-4016-af64-77a3b8616672 的路径点
[CalculateElementConnectPoint] ✅ 选择分支ViewModel右上连接点(索引2): 396,354.5
[CalculateElementConnectPoint] ✅ 选择分支ViewModel左下连接点(索引1): 396,354.5
[AddConnection] 延迟更新 - 源对象类型: SFCBranchViewModel, 目标对象类型: SFCBranchViewModel
[AddConnection] 延迟更新 - 源位置: 221,319.5, 目标位置: 367,319.5
[AddConnection] 延迟更新后的路径点: 396,354.5 -> 396,354.5
[AddConnection] PathPoints集合已更新，应该触发重新评估
