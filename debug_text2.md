[InsertParallelBranchAfterTransition] 选中元素: 转换3
[并行分支] 首次插入，现有分支: 0个
[GetActualConnectPointPosition] 转换条件连接点: 索引0, 位置240.13636363636414,434.24545454545506
[InsertParallelBranchAfterTransition] 动态计算首个并行分支位置: 连接点240.13636363636414,434.24545454545506, 分支位置199.13636363636414,448.24545454545506
[并行分支] 新分支位置: 199.13636363636414,448.24545454545506
[并行分支] 创建: Initial, 位置: 199.13636363636414,448.24545454545506
[分支集合] 添加分支，位置: 199.13636363636414,448.24545454545506
[SFCBranchViewModel] 初始化适配器: BranchType=Selection, ElementType=SelectionBranch
[SFCBranchViewModel] ✅ 选择分支适配器配置完成，数量: 4
[SFCBranchViewModel] 🔧 索引2适配器类型: Output (修复为Output)
[SFCBranchViewModel] 初始化适配器: BranchType=Parallel, ElementType=ParallelBranch
[SFCBranchViewModel] ✅ 并行分支适配器配置完成，数量: 3
[SFCBranchViewModel] 🔄 分支类型变更为Parallel，重新初始化适配器，适配器数量: 3
[分支集合] ViewModel位置: 199.13636363636414,448.24545454545506
[SFCCanvas] 为新创建的分支添加位置变化监听: 193f1508-b89e-4086-834a-6bbf63f93f2e
[AddConnection] 开始执行: cdcd7d6d-2be7-4c94-ae47-248479f91567 -> 193f1508-b89e-4086-834a-6bbf63f93f2e
[AddConnection] 对象查找结果: sourceObject=SFCTransitionModel, targetObject=SFCBranchModel
[AddConnection] 位置获取: 源ViewModel位置=179.83636363636413,404.24545454545506, 源Model位置=213,354.5
[AddConnection] 位置获取: 目标ViewModel位置=199.13636363636414,448.24545454545506, 目标Model位置=199.13636363636414,448.24545454545506
[GetPreciseConnectPointPosition] ⚠️ 回退到计算方法
[GetPreciseConnectPointPosition] ⚠️ 回退到计算方法
[CalculateElementConnectPoint] SFCBranchModel: 类型=Parallel, 位置=199.13636363636414,448.24545454545506, 输出点=False
[对比调试] 如果是步骤输入点，位置应该是: 249.13636363636414,454.24545454545506
[CalculateElementConnectPoint] ✅ 并行分支上端输入点(索引0): 241.13636363636414,444.24545454545506
[AddConnection] 创建连接: cdcd7d6d-2be7-4c94-ae47-248479f91567 -> 193f1508-b89e-4086-834a-6bbf63f93f2e
[AddConnection] 源位置: 179.83636363636413,404.24545454545506, 目标位置: 199.13636363636414,448.24545454545506
[AddConnection] 源对象类型: SFCTransitionModel, 目标对象类型: SFCBranchModel
[AddConnection] 源连接点: 240.13636363636414,434.24545454545506, 目标连接点: 241.13636363636414,444.24545454545506
[AddConnection] 连接创建完成，路径点数量: 2
[AddConnectionViewModel] 连接 0a91a047-c2e1-4fe7-ac65-bf01e08344da 路径点数量: 2
[AddConnectionViewModel] ViewModel路径点数量: 2
[AddConnectionViewModel] 起点: 240.13636363636414,434.24545454545506, 终点: 241.13636363636414,444.24545454545506
[AddConnection] 🔄 开始更新连接点状态: cdcd7d6d-2be7-4c94-ae47-248479f91567 -> 193f1508-b89e-4086-834a-6bbf63f93f2e
[UpdateElementConnectPointState] 🔍 开始更新连接点状态:
  元素类型: SFCTransitionViewModel
  连接点索引: 0
  是否输入: False
  连接ID: 0a91a047-c2e1-4fe7-ac65-bf01e08344da
[UpdateElementConnectPointState] ✅ 连接点状态更新成功: SFCTransitionViewModel, Direction=Output, Index=0, AdapterIndex=0, ConnectionId: 0a91a047-c2e1-4fe7-ac65-bf01e08344da
[UpdateElementConnectPointState] 🔍 开始更新连接点状态:
  元素类型: SFCBranchViewModel
  连接点索引: 0
  是否输入: True
  连接ID: 0a91a047-c2e1-4fe7-ac65-bf01e08344da
[UpdateElementConnectPointState] 🎯 找到分支元素适配器: 193f1508-b89e-4086-834a-6bbf63f93f2e, 分支类型: Parallel, 适配器数量: 3
[UpdateElementConnectPointState] ✅ 连接点状态更新成功: SFCBranchViewModel, Direction=Input, Index=0, AdapterIndex=0, ConnectionId: 0a91a047-c2e1-4fe7-ac65-bf01e08344da
[UpdateConnectPointStates] 连接点状态更新完成: cdcd7d6d-2be7-4c94-ae47-248479f91567[0] -> 193f1508-b89e-4086-834a-6bbf63f93f2e[0]
[并行分支] 跳过垂直对齐，保持计算位置: 199.13636363636414,448.24545454545506
[SFCCanvas] 为新创建的步骤添加位置变化监听: 8d5de8c0-65fe-4692-86ce-a1a3df1a60ca
[InsertParallelBranchAfterTransition] 创建左分支步骤: 8d5de8c0-65fe-4692-86ce-a1a3df1a60ca
[InsertParallelBranchAfterTransition] 创建左侧连接: 193f1508-b89e-4086-834a-6bbf63f93f2e -> 8d5de8c0-65fe-4692-86ce-a1a3df1a60ca, 源索引=1, 目标索引=0
[AddConnection] 开始执行: 193f1508-b89e-4086-834a-6bbf63f93f2e -> 8d5de8c0-65fe-4692-86ce-a1a3df1a60ca
[AddConnection] 对象查找结果: sourceObject=SFCBranchModel, targetObject=SFCStepModel
[AddConnection] 位置获取: 源ViewModel位置=199.13636363636414,448.24545454545506, 源Model位置=199.13636363636414,448.24545454545506
[AddConnection] 位置获取: 目标ViewModel位置=189.63636363636414,462.24545454545506, 目标Model位置=189.63636363636414,462.24545454545506
[GetPreciseConnectPointPosition] ⚠️ 回退到计算方法
[CalculateElementConnectPoint] SFCBranchModel: 类型=Parallel, 位置=199.13636363636414,448.24545454545506, 输出点=True
[CalculateElementConnectPoint] ✅ 并行分支左侧连接点(索引1): 241.13636363636414,472.74545454545506
[GetPreciseConnectPointPosition] ⚠️ 回退到计算方法
[AddConnection] 创建连接: 193f1508-b89e-4086-834a-6bbf63f93f2e -> 8d5de8c0-65fe-4692-86ce-a1a3df1a60ca
[AddConnection] 源位置: 199.13636363636414,448.24545454545506, 目标位置: 189.63636363636414,462.24545454545506
[AddConnection] 源对象类型: SFCBranchModel, 目标对象类型: SFCStepModel
[AddConnection] 源连接点: 241.13636363636414,472.74545454545506, 目标连接点: 239.63636363636414,468.24545454545506
[AddConnection] 连接创建完成，路径点数量: 2
[AddConnectionViewModel] 连接 1e2781ed-ed3d-49a1-8488-59ffffc35f69 路径点数量: 2
[AddConnectionViewModel] ViewModel路径点数量: 2
[AddConnectionViewModel] 起点: 241.13636363636414,472.74545454545506, 终点: 239.63636363636414,468.24545454545506
[AddConnection] 🔄 开始更新连接点状态: 193f1508-b89e-4086-834a-6bbf63f93f2e -> 8d5de8c0-65fe-4692-86ce-a1a3df1a60ca
[UpdateElementConnectPointState] 🔍 开始更新连接点状态:
  元素类型: SFCBranchViewModel
  连接点索引: 1
  是否输入: False
  连接ID: 1e2781ed-ed3d-49a1-8488-59ffffc35f69
[UpdateElementConnectPointState] 🎯 找到分支元素适配器: 193f1508-b89e-4086-834a-6bbf63f93f2e, 分支类型: Parallel, 适配器数量: 3
[UpdateElementConnectPointState] ✅ 连接点状态更新成功: SFCBranchViewModel, Direction=Output, Index=1, AdapterIndex=1, ConnectionId: 1e2781ed-ed3d-49a1-8488-59ffffc35f69
[UpdateElementConnectPointState] 🔍 开始更新连接点状态:
  元素类型: SFCStepViewModel
  连接点索引: 0
  是否输入: True
  连接ID: 1e2781ed-ed3d-49a1-8488-59ffffc35f69
[UpdateElementConnectPointState] ✅ 连接点状态更新成功: SFCStepViewModel, Direction=Input, Index=0, AdapterIndex=0, ConnectionId: 1e2781ed-ed3d-49a1-8488-59ffffc35f69
[UpdateConnectPointStates] 连接点状态更新完成: 193f1508-b89e-4086-834a-6bbf63f93f2e[1] -> 8d5de8c0-65fe-4692-86ce-a1a3df1a60ca[0]
[SFCCanvas] 为新创建的步骤添加位置变化监听: 87ecdef7-fe8f-46c3-a981-d6d0435c89f4
[InsertParallelBranchAfterTransition] 创建右分支步骤: 87ecdef7-fe8f-46c3-a981-d6d0435c89f4
[InsertParallelBranchAfterTransition] 创建右侧连接: 193f1508-b89e-4086-834a-6bbf63f93f2e -> 87ecdef7-fe8f-46c3-a981-d6d0435c89f4, 源索引=2, 目标索引=0
[AddConnection] 开始执行: 193f1508-b89e-4086-834a-6bbf63f93f2e -> 87ecdef7-fe8f-46c3-a981-d6d0435c89f4
[AddConnection] 对象查找结果: sourceObject=SFCBranchModel, targetObject=SFCStepModel
[AddConnection] 位置获取: 源ViewModel位置=199.13636363636414,448.24545454545506, 源Model位置=199.13636363636414,448.24545454545506
[AddConnection] 位置获取: 目标ViewModel位置=337.13636363636414,462.24545454545506, 目标Model位置=337.13636363636414,462.24545454545506
[GetPreciseConnectPointPosition] ⚠️ 回退到计算方法
[CalculateElementConnectPoint] SFCBranchModel: 类型=Parallel, 位置=199.13636363636414,448.24545454545506, 输出点=True
[CalculateElementConnectPoint] ✅ 并行分支默认右侧连接点(索引2): 387.13636363636414,472.74545454545506
[GetPreciseConnectPointPosition] ⚠️ 回退到计算方法
[AddConnection] 创建连接: 193f1508-b89e-4086-834a-6bbf63f93f2e -> 87ecdef7-fe8f-46c3-a981-d6d0435c89f4
[AddConnection] 源位置: 199.13636363636414,448.24545454545506, 目标位置: 337.13636363636414,462.24545454545506
[AddConnection] 源对象类型: SFCBranchModel, 目标对象类型: SFCStepModel
[AddConnection] 源连接点: 387.13636363636414,472.74545454545506, 目标连接点: 387.13636363636414,468.24545454545506
[AddConnection] 连接创建完成，路径点数量: 2
[AddConnectionViewModel] 连接 ab4b4799-9382-4973-af9d-64a97f70813c 路径点数量: 2
[AddConnectionViewModel] ViewModel路径点数量: 2
[AddConnectionViewModel] 起点: 387.13636363636414,472.74545454545506, 终点: 387.13636363636414,468.24545454545506
[AddConnection] 🔄 开始更新连接点状态: 193f1508-b89e-4086-834a-6bbf63f93f2e -> 87ecdef7-fe8f-46c3-a981-d6d0435c89f4
[UpdateElementConnectPointState] 🔍 开始更新连接点状态:
  元素类型: SFCBranchViewModel
  连接点索引: 2
  是否输入: False
  连接ID: ab4b4799-9382-4973-af9d-64a97f70813c
[UpdateElementConnectPointState] 🎯 找到分支元素适配器: 193f1508-b89e-4086-834a-6bbf63f93f2e, 分支类型: Parallel, 适配器数量: 3
[UpdateElementConnectPointState] ✅ 连接点状态更新成功: SFCBranchViewModel, Direction=Output, Index=2, AdapterIndex=2, ConnectionId: ab4b4799-9382-4973-af9d-64a97f70813c
[UpdateElementConnectPointState] 🔍 开始更新连接点状态:
  元素类型: SFCStepViewModel
  连接点索引: 0
  是否输入: True
  连接ID: ab4b4799-9382-4973-af9d-64a97f70813c
[UpdateElementConnectPointState] ✅ 连接点状态更新成功: SFCStepViewModel, Direction=Input, Index=0, AdapterIndex=0, ConnectionId: ab4b4799-9382-4973-af9d-64a97f70813c
[UpdateConnectPointStates] 连接点状态更新完成: 193f1508-b89e-4086-834a-6bbf63f93f2e[2] -> 87ecdef7-fe8f-46c3-a981-d6d0435c89f4[0]
System.Windows.Data Error: 40 : BindingExpression path error: 'Description' property not found on 'object' ''SFCBranchViewModel' (HashCode=21771268)'. BindingExpression:Path=SelectedElement.Description; DataItem='EnhancedSFCViewModel' (HashCode=34232944); target element is 'TextBlock' (Name=''); target property is 'Text' (type 'String')
[SFCParallelBranchView] 开始设置连接点ElementId: 193f1508-b89e-4086-834a-6bbf63f93f2e
[SFCParallelBranchView] ✅ LeftTop连接点ElementId设置: 193f1508-b89e-4086-834a-6bbf63f93f2e
[SFCParallelBranchView] ✅ LeftParallel连接点ElementId设置: 193f1508-b89e-4086-834a-6bbf63f93f2e
[SFCParallelBranchView] ✅ RightParallel连接点ElementId设置: 193f1508-b89e-4086-834a-6bbf63f93f2e
[SFCParallelBranchView] ✅ 已绑定LeftTop连接点适配器: 193f1508-b89e-4086-834a-6bbf63f93f2e
[SFCParallelBranchView] ✅ 已绑定LeftParallel连接点适配器: 193f1508-b89e-4086-834a-6bbf63f93f2e
[SFCParallelBranchView] ✅ 已绑定RightParallel连接点适配器: 193f1508-b89e-4086-834a-6bbf63f93f2e
[SFCParallelBranchView] 开始同步现有连接状态: 193f1508-b89e-4086-834a-6bbf63f93f2e
[SFCParallelBranchView] 找到 3 个相关连接
[SFCParallelBranchView] ✅ 同步输入连接: 0a91a047-c2e1-4fe7-ac65-bf01e08344da, 索引: 0
[SFCParallelBranchView] ✅ 同步输出连接: 1e2781ed-ed3d-49a1-8488-59ffffc35f69, 索引: 1
[SFCParallelBranchView] ✅ 同步输出连接: ab4b4799-9382-4973-af9d-64a97f70813c, 索引: 2
System.Windows.Data Error: 40 : BindingExpression path error: 'IsInitialStep' property not found on 'object' ''SFCBranchViewModel' (HashCode=21771268)'. BindingExpression:Path=IsInitialStep; DataItem='SFCBranchViewModel' (HashCode=21771268); target element is 'SFCConnectPoint' (Name='LeftTopConnectPoint'); target property is 'NoTarget' (type 'Object')
System.Windows.Data Error: 40 : BindingExpression path error: 'IsInitialStep' property not found on 'object' ''SFCBranchViewModel' (HashCode=21771268)'. BindingExpression:Path=IsInitialStep; DataItem='SFCBranchViewModel' (HashCode=21771268); target element is 'SFCConnectPoint' (Name='LeftBottomConnectPoint'); target property is 'NoTarget' (type 'Object')
System.Windows.Data Error: 40 : BindingExpression path error: 'IsInitialStep' property not found on 'object' ''SFCBranchViewModel' (HashCode=21771268)'. BindingExpression:Path=IsInitialStep; DataItem='SFCBranchViewModel' (HashCode=21771268); target element is 'SFCConnectPoint' (Name='TopConnectPoint1'); target property is 'NoTarget' (type 'Object')
System.Windows.Data Error: 40 : BindingExpression path error: 'IsInitialStep' property not found on 'object' ''SFCBranchViewModel' (HashCode=21771268)'. BindingExpression:Path=IsInitialStep; DataItem='SFCBranchViewModel' (HashCode=21771268); target element is 'SFCConnectPoint' (Name='TopConnectPoint2'); target property is 'NoTarget' (type 'Object')
System.Windows.Data Error: 40 : BindingExpression path error: 'IsInitialStep' property not found on 'object' ''SFCBranchViewModel' (HashCode=21771268)'. BindingExpression:Path=IsInitialStep; DataItem='SFCBranchViewModel' (HashCode=21771268); target element is 'SFCConnectPoint' (Name='LeftTopConnectPoint'); target property is 'NoTarget' (type 'Object')
System.Windows.Data Error: 40 : BindingExpression path error: 'IsInitialStep' property not found on 'object' ''SFCBranchViewModel' (HashCode=21771268)'. BindingExpression:Path=IsInitialStep; DataItem='SFCBranchViewModel' (HashCode=21771268); target element is 'SFCConnectPoint' (Name='LeftParallelPoint'); target property is 'NoTarget' (type 'Object')
System.Windows.Data Error: 40 : BindingExpression path error: 'IsInitialStep' property not found on 'object' ''SFCBranchViewModel' (HashCode=21771268)'. BindingExpression:Path=IsInitialStep; DataItem='SFCBranchViewModel' (HashCode=21771268); target element is 'SFCConnectPoint' (Name='RightParallelPoint'); target property is 'NoTarget' (type 'Object')
[SFCParallelBranchView] 开始设置连接点ElementId: 193f1508-b89e-4086-834a-6bbf63f93f2e
[SFCParallelBranchView] ✅ LeftTop连接点ElementId设置: 193f1508-b89e-4086-834a-6bbf63f93f2e
[SFCParallelBranchView] ✅ LeftParallel连接点ElementId设置: 193f1508-b89e-4086-834a-6bbf63f93f2e
[SFCParallelBranchView] ✅ RightParallel连接点ElementId设置: 193f1508-b89e-4086-834a-6bbf63f93f2e
[SFCParallelBranchView] ✅ 已绑定LeftTop连接点适配器: 193f1508-b89e-4086-834a-6bbf63f93f2e
[SFCParallelBranchView] ✅ 已绑定LeftParallel连接点适配器: 193f1508-b89e-4086-834a-6bbf63f93f2e
[SFCParallelBranchView] ✅ 已绑定RightParallel连接点适配器: 193f1508-b89e-4086-834a-6bbf63f93f2e
[SFCParallelBranchView] 开始同步现有连接状态: 193f1508-b89e-4086-834a-6bbf63f93f2e
[SFCParallelBranchView] 找到 3 个相关连接
[SFCParallelBranchView] ✅ 同步输入连接: 0a91a047-c2e1-4fe7-ac65-bf01e08344da, 索引: 0
[SFCParallelBranchView] ✅ 同步输出连接: 1e2781ed-ed3d-49a1-8488-59ffffc35f69, 索引: 1
[SFCParallelBranchView] ✅ 同步输出连接: ab4b4799-9382-4973-af9d-64a97f70813c, 索引: 2
System.Windows.Data Error: 4 : Cannot find source for binding with reference 'RelativeSource FindAncestor, AncestorType='System.Windows.Controls.UserControl', AncestorLevel='1''. BindingExpression:Path=DataContext.InsertSelectionBranchFromAnyPartCommand; DataItem=null; target element is 'MenuItem' (Name=''); target property is 'Command' (type 'ICommand')
System.Windows.Data Error: 4 : Cannot find source for binding with reference 'RelativeSource FindAncestor, AncestorType='System.Windows.Controls.UserControl', AncestorLevel='1''. BindingExpression:Path=DataContext.InsertSiblingBranchCommand; DataItem=null; target element is 'MenuItem' (Name=''); target property is 'Command' (type 'ICommand')
System.Windows.Data Error: 4 : Cannot find source for binding with reference 'RelativeSource FindAncestor, AncestorType='System.Windows.Controls.UserControl', AncestorLevel='1''. BindingExpression:Path=DataContext.InsertParallelBranchFromAnyPartCommand; DataItem=null; target element is 'MenuItem' (Name=''); target property is 'Command' (type 'ICommand')
[CreateConnectionPath] 连接 0a91a047-c2e1-4fe7-ac65-bf01e08344da 连接点不重叠，创建连接线
[CreateConnectionPath] 起点: (240.1, 434.2), 终点: (241.1, 444.2), 距离: 10.0px
[贝塞尔曲线创建] 起点: (240.1, 434.2), 终点: (241.1, 444.2)
[贝塞尔曲线创建] Y差异: 10.0px, X差异: 1.0px, 控制点偏移: 8.0px
[贝塞尔曲线创建] PathPoints[0]: (240.1, 434.2), PathPoints[1]: (241.1, 444.2)
添加连接线: 0a91a047-c2e1-4fe7-ac65-bf01e08344da
[AddConnection] 延迟更新连接线 0a91a047-c2e1-4fe7-ac65-bf01e08344da 的路径点
[PathPoints集合变化] 连接 0a91a047-c2e1-4fe7-ac65-bf01e08344da 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 0a91a047-c2e1-4fe7-ac65-bf01e08344da 的PathPoints已更新，重新评估连接线
[PathPoints集合变化] 连接 0a91a047-c2e1-4fe7-ac65-bf01e08344da 的PathPoints已更新，重新评估连接线
[AddConnection] 延迟更新 - 源对象类型: SFCTransitionViewModel, 目标对象类型: SFCBranchViewModel
[AddConnection] 延迟更新 - 源位置: 179.83636363636413,404.24545454545506, 目标位置: 199.13636363636414,448.24545454545506
[AddConnection] 延迟更新后的路径点: 240.13636363636414,434.24545454545506 -> 241.13636363636414,444.24545454545506
[AddConnection] PathPoints集合已更新，应该触发重新评估
[连接点重叠检测] 点1: (241.1, 472.7), 点2: (239.6, 468.2), 距离: 4.7px, 重叠: True
[CreateConnectionPath] 连接 1e2781ed-ed3d-49a1-8488-59ffffc35f69 连接点重叠，隐藏连接线
[CreateConnectionPath] 起点: (241.1, 472.7), 终点: (239.6, 468.2)
连接 1e2781ed-ed3d-49a1-8488-59ffffc35f69 连接点重叠，使用连接点重叠方式
[AddConnection] 延迟更新连接线 1e2781ed-ed3d-49a1-8488-59ffffc35f69 的路径点
[CalculateElementConnectPoint] 🔵 并行分支左侧连接点(索引1): 241.13636363636414,472.74545454545506
[AddConnection] 延迟更新 - 源对象类型: SFCBranchViewModel, 目标对象类型: SFCStepViewModel
[AddConnection] 延迟更新 - 源位置: 199.13636363636414,448.24545454545506, 目标位置: 189.63636363636414,462.24545454545506
[AddConnection] 延迟更新后的路径点: 241.13636363636414,472.74545454545506 -> 239.63636363636414,468.24545454545506
[AddConnection] PathPoints集合已更新，应该触发重新评估
[连接点重叠检测] 点1: (387.1, 472.7), 点2: (387.1, 468.2), 距离: 4.5px, 重叠: True
[CreateConnectionPath] 连接 ab4b4799-9382-4973-af9d-64a97f70813c 连接点重叠，隐藏连接线
[CreateConnectionPath] 起点: (387.1, 472.7), 终点: (387.1, 468.2)
连接 ab4b4799-9382-4973-af9d-64a97f70813c 连接点重叠，使用连接点重叠方式
[AddConnection] 延迟更新连接线 ab4b4799-9382-4973-af9d-64a97f70813c 的路径点
[AddConnection] 延迟更新 - 源对象类型: SFCBranchViewModel, 目标对象类型: SFCStepViewModel
[AddConnection] 延迟更新 - 源位置: 199.13636363636414,448.24545454545506, 目标位置: 337.13636363636414,462.24545454545506
[AddConnection] 延迟更新后的路径点: 387.13636363636414,472.74545454545506 -> 387.13636363636414,468.24545454545506
[AddConnection] PathPoints集合已更新，应该触发重新评估
[SFCCanvas] 位置变化: SFCBranchViewModel.IsSelected
[SFCParallelBranchView] 拖动更新位置: 193f1508-b89e-4086-834a-6bbf63f93f2e -> 199.13636363636414,448.24545454545506
System.Windows.Data Error: 40 : BindingExpression path error: 'Description' property not found on 'object' ''SFCBranchViewModel' (HashCode=21771268)'. BindingExpression:Path=SelectedElement.Description; DataItem='EnhancedSFCViewModel' (HashCode=34232944); target element is 'TextBlock' (Name=''); target property is 'Text' (type 'String')
[分支链] 检查分支: 193f1508-b89e-4086-834a-6bbf63f93f2e, 位置: 199.13636363636414,448.24545454545506
[分支链] 到达末端: 193f1508-b89e-4086-834a-6bbf63f93f2e
[分支链] 最右侧分支位置: 199.13636363636414
[InsertSiblingParallelBranch] 计算的分支位置: 346.63636363636414,448.24545454545506
[SFCBranchViewModel] 初始化适配器: BranchType=Selection, ElementType=SelectionBranch
[SFCBranchViewModel] ✅ 选择分支适配器配置完成，数量: 4
[SFCBranchViewModel] 🔧 索引2适配器类型: Output (修复为Output)
[SFCBranchViewModel] 初始化适配器: BranchType=Parallel, ElementType=ParallelBranch
[SFCBranchViewModel] ✅ 并行分支适配器配置完成，数量: 3
[SFCBranchViewModel] 🔄 分支类型变更为Parallel，重新初始化适配器，适配器数量: 3
[分支链] 检查分支: 193f1508-b89e-4086-834a-6bbf63f93f2e, 位置: 199.13636363636414,448.24545454545506
[分支链] 到达末端: 193f1508-b89e-4086-834a-6bbf63f93f2e
[分支链] 最右侧分支位置: 199.13636363636414
[SFCCanvas] 位置变化: SFCBranchViewModel.NextBranchId
[SFCCanvas] 为新创建的分支添加位置变化监听: 7a774e43-90a9-4ce4-9fd4-80b96dc0d06e
[CreateBranchChainConnection] 并行分支链：右侧双线（索引0）-> 左侧双线（索引1）
[CreateBranchChainConnection] 分支链连接: Parallel 源索引0 -> Parallel 目标索引1
[CreateBranchChainConnection] 创建分支链连接: 193f1508-b89e-4086-834a-6bbf63f93f2e -> 7a774e43-90a9-4ce4-9fd4-80b96dc0d06e
[AddConnection] 开始执行: 193f1508-b89e-4086-834a-6bbf63f93f2e -> 7a774e43-90a9-4ce4-9fd4-80b96dc0d06e
[AddConnection] 对象查找结果: sourceObject=SFCBranchModel, targetObject=SFCBranchModel
[AddConnection] 位置获取: 源ViewModel位置=199.13636363636414,448.24545454545506, 源Model位置=199.13636363636414,448.24545454545506
[AddConnection] 位置获取: 目标ViewModel位置=346.63636363636414,448.24545454545506, 目标Model位置=346.63636363636414,448.24545454545506
[GetPreciseConnectPointPosition] ⚠️ 回退到计算方法
[CalculateElementConnectPoint] SFCBranchModel: 类型=Parallel, 位置=199.13636363636414,448.24545454545506, 输出点=True
[CalculateElementConnectPoint] ✅ 并行分支右侧连接点(索引0): 387.13636363636414,472.74545454545506
[GetPreciseConnectPointPosition] ⚠️ 回退到计算方法
[CalculateElementConnectPoint] SFCBranchModel: 类型=Parallel, 位置=346.63636363636414,448.24545454545506, 输出点=False
[对比调试] 如果是步骤输入点，位置应该是: 396.63636363636414,454.24545454545506
[CalculateElementConnectPoint] ✅ 并行分支左侧连接点(索引1): 388.63636363636414,472.74545454545506
[AddConnection] 创建连接: 193f1508-b89e-4086-834a-6bbf63f93f2e -> 7a774e43-90a9-4ce4-9fd4-80b96dc0d06e
[AddConnection] 源位置: 199.13636363636414,448.24545454545506, 目标位置: 346.63636363636414,448.24545454545506
[AddConnection] 源对象类型: SFCBranchModel, 目标对象类型: SFCBranchModel
[AddConnection] 源连接点: 387.13636363636414,472.74545454545506, 目标连接点: 388.63636363636414,472.74545454545506
[AddConnection] 连接创建完成，路径点数量: 2
[AddConnectionViewModel] 连接 98031ff4-a506-42ea-863f-1a1eb39eeade 路径点数量: 2
[AddConnectionViewModel] ViewModel路径点数量: 2
[AddConnectionViewModel] 起点: 387.13636363636414,472.74545454545506, 终点: 388.63636363636414,472.74545454545506
[AddConnection] 🔄 开始更新连接点状态: 193f1508-b89e-4086-834a-6bbf63f93f2e -> 7a774e43-90a9-4ce4-9fd4-80b96dc0d06e
[UpdateElementConnectPointState] 🔍 开始更新连接点状态:
  元素类型: SFCBranchViewModel
  连接点索引: 0
  是否输入: False
  连接ID: 98031ff4-a506-42ea-863f-1a1eb39eeade
[UpdateElementConnectPointState] 🎯 找到分支元素适配器: 193f1508-b89e-4086-834a-6bbf63f93f2e, 分支类型: Parallel, 适配器数量: 3
[UpdateElementConnectPointState] ⚠️ 精确匹配失败，使用Direction匹配: Direction=Output, Index=0
[UpdateElementConnectPointState] ✅ 连接点状态更新成功: SFCBranchViewModel, Direction=Output, Index=0, AdapterIndex=1, ConnectionId: 98031ff4-a506-42ea-863f-1a1eb39eeade
[UpdateElementConnectPointState] 🔍 开始更新连接点状态:
  元素类型: SFCBranchViewModel
  连接点索引: 1
  是否输入: True
  连接ID: 98031ff4-a506-42ea-863f-1a1eb39eeade
[UpdateElementConnectPointState] 🎯 找到分支元素适配器: 7a774e43-90a9-4ce4-9fd4-80b96dc0d06e, 分支类型: Parallel, 适配器数量: 3
[UpdateElementConnectPointState] ⚠️ 精确匹配失败，使用Direction匹配: Direction=Input, Index=1
[UpdateElementConnectPointState] ✅ 连接点状态更新成功: SFCBranchViewModel, Direction=Input, Index=1, AdapterIndex=0, ConnectionId: 98031ff4-a506-42ea-863f-1a1eb39eeade
[UpdateConnectPointStates] 连接点状态更新完成: 193f1508-b89e-4086-834a-6bbf63f93f2e[0] -> 7a774e43-90a9-4ce4-9fd4-80b96dc0d06e[1]
[InsertSiblingParallelBranch] 创建并行分支链连接线: 193f1508-b89e-4086-834a-6bbf63f93f2e -> 7a774e43-90a9-4ce4-9fd4-80b96dc0d06e
[SFCCanvas] 为新创建的步骤添加位置变化监听: 2366fcf5-29f1-4a74-810a-f8d74bbec42e
[InsertSiblingParallelBranch] 创建右分支步骤: 2366fcf5-29f1-4a74-810a-f8d74bbec42e
[AddConnection] 开始执行: 7a774e43-90a9-4ce4-9fd4-80b96dc0d06e -> 2366fcf5-29f1-4a74-810a-f8d74bbec42e
[AddConnection] 对象查找结果: sourceObject=SFCBranchModel, targetObject=SFCStepModel
[AddConnection] 位置获取: 源ViewModel位置=346.63636363636414,448.24545454545506, 源Model位置=346.63636363636414,448.24545454545506
[AddConnection] 位置获取: 目标ViewModel位置=484.63636363636414,462.24545454545506, 目标Model位置=484.63636363636414,462.24545454545506
[GetPreciseConnectPointPosition] ⚠️ 回退到计算方法
[CalculateElementConnectPoint] SFCBranchModel: 类型=Parallel, 位置=346.63636363636414,448.24545454545506, 输出点=True
[CalculateElementConnectPoint] ✅ 并行分支默认右侧连接点(索引2): 534.6363636363642,472.74545454545506
[GetPreciseConnectPointPosition] ⚠️ 回退到计算方法
[AddConnection] 创建连接: 7a774e43-90a9-4ce4-9fd4-80b96dc0d06e -> 2366fcf5-29f1-4a74-810a-f8d74bbec42e
[AddConnection] 源位置: 346.63636363636414,448.24545454545506, 目标位置: 484.63636363636414,462.24545454545506
[AddConnection] 源对象类型: SFCBranchModel, 目标对象类型: SFCStepModel
[AddConnection] 源连接点: 534.6363636363642,472.74545454545506, 目标连接点: 534.6363636363642,468.24545454545506
[AddConnection] 连接创建完成，路径点数量: 2
[AddConnectionViewModel] 连接 fbf2d79e-0d84-4288-a3fb-2eda35e3cd8c 路径点数量: 2
[AddConnectionViewModel] ViewModel路径点数量: 2
[AddConnectionViewModel] 起点: 534.6363636363642,472.74545454545506, 终点: 534.6363636363642,468.24545454545506
[AddConnection] 🔄 开始更新连接点状态: 7a774e43-90a9-4ce4-9fd4-80b96dc0d06e -> 2366fcf5-29f1-4a74-810a-f8d74bbec42e
[UpdateElementConnectPointState] 🔍 开始更新连接点状态:
  元素类型: SFCBranchViewModel
  连接点索引: 2
  是否输入: False
  连接ID: fbf2d79e-0d84-4288-a3fb-2eda35e3cd8c
[UpdateElementConnectPointState] 🎯 找到分支元素适配器: 7a774e43-90a9-4ce4-9fd4-80b96dc0d06e, 分支类型: Parallel, 适配器数量: 3
[UpdateElementConnectPointState] ✅ 连接点状态更新成功: SFCBranchViewModel, Direction=Output, Index=2, AdapterIndex=2, ConnectionId: fbf2d79e-0d84-4288-a3fb-2eda35e3cd8c
[UpdateElementConnectPointState] 🔍 开始更新连接点状态:
  元素类型: SFCStepViewModel
  连接点索引: 0
  是否输入: True
  连接ID: fbf2d79e-0d84-4288-a3fb-2eda35e3cd8c
[UpdateElementConnectPointState] ✅ 连接点状态更新成功: SFCStepViewModel, Direction=Input, Index=0, AdapterIndex=0, ConnectionId: fbf2d79e-0d84-4288-a3fb-2eda35e3cd8c
[UpdateConnectPointStates] 连接点状态更新完成: 7a774e43-90a9-4ce4-9fd4-80b96dc0d06e[2] -> 2366fcf5-29f1-4a74-810a-f8d74bbec42e[0]
System.Windows.Data Error: 40 : BindingExpression path error: 'Description' property not found on 'object' ''SFCBranchViewModel' (HashCode=42949880)'. BindingExpression:Path=SelectedElement.Description; DataItem='EnhancedSFCViewModel' (HashCode=34232944); target element is 'TextBlock' (Name=''); target property is 'Text' (type 'String')
[SFCParallelBranchView] 开始设置连接点ElementId: 7a774e43-90a9-4ce4-9fd4-80b96dc0d06e
[SFCParallelBranchView] ✅ LeftTop连接点ElementId设置: 7a774e43-90a9-4ce4-9fd4-80b96dc0d06e
[SFCParallelBranchView] ✅ LeftParallel连接点ElementId设置: 7a774e43-90a9-4ce4-9fd4-80b96dc0d06e
[SFCParallelBranchView] ✅ RightParallel连接点ElementId设置: 7a774e43-90a9-4ce4-9fd4-80b96dc0d06e
[SFCParallelBranchView] ✅ 已绑定LeftTop连接点适配器: 7a774e43-90a9-4ce4-9fd4-80b96dc0d06e
[SFCParallelBranchView] ✅ 已绑定LeftParallel连接点适配器: 7a774e43-90a9-4ce4-9fd4-80b96dc0d06e
[SFCParallelBranchView] ✅ 已绑定RightParallel连接点适配器: 7a774e43-90a9-4ce4-9fd4-80b96dc0d06e
[SFCParallelBranchView] 开始同步现有连接状态: 7a774e43-90a9-4ce4-9fd4-80b96dc0d06e
[SFCParallelBranchView] 找到 2 个相关连接
[SFCParallelBranchView] ✅ 同步输出连接: fbf2d79e-0d84-4288-a3fb-2eda35e3cd8c, 索引: 2
System.Windows.Data Error: 40 : BindingExpression path error: 'IsInitialStep' property not found on 'object' ''SFCBranchViewModel' (HashCode=42949880)'. BindingExpression:Path=IsInitialStep; DataItem='SFCBranchViewModel' (HashCode=42949880); target element is 'SFCConnectPoint' (Name='LeftTopConnectPoint'); target property is 'NoTarget' (type 'Object')
System.Windows.Data Error: 40 : BindingExpression path error: 'IsInitialStep' property not found on 'object' ''SFCBranchViewModel' (HashCode=42949880)'. BindingExpression:Path=IsInitialStep; DataItem='SFCBranchViewModel' (HashCode=42949880); target element is 'SFCConnectPoint' (Name='LeftBottomConnectPoint'); target property is 'NoTarget' (type 'Object')
System.Windows.Data Error: 40 : BindingExpression path error: 'IsInitialStep' property not found on 'object' ''SFCBranchViewModel' (HashCode=42949880)'. BindingExpression:Path=IsInitialStep; DataItem='SFCBranchViewModel' (HashCode=42949880); target element is 'SFCConnectPoint' (Name='TopConnectPoint1'); target property is 'NoTarget' (type 'Object')
System.Windows.Data Error: 40 : BindingExpression path error: 'IsInitialStep' property not found on 'object' ''SFCBranchViewModel' (HashCode=42949880)'. BindingExpression:Path=IsInitialStep; DataItem='SFCBranchViewModel' (HashCode=42949880); target element is 'SFCConnectPoint' (Name='TopConnectPoint2'); target property is 'NoTarget' (type 'Object')
System.Windows.Data Error: 40 : BindingExpression path error: 'IsInitialStep' property not found on 'object' ''SFCBranchViewModel' (HashCode=42949880)'. BindingExpression:Path=IsInitialStep; DataItem='SFCBranchViewModel' (HashCode=42949880); target element is 'SFCConnectPoint' (Name='LeftTopConnectPoint'); target property is 'NoTarget' (type 'Object')
System.Windows.Data Error: 40 : BindingExpression path error: 'IsInitialStep' property not found on 'object' ''SFCBranchViewModel' (HashCode=42949880)'. BindingExpression:Path=IsInitialStep; DataItem='SFCBranchViewModel' (HashCode=42949880); target element is 'SFCConnectPoint' (Name='LeftParallelPoint'); target property is 'NoTarget' (type 'Object')
System.Windows.Data Error: 40 : BindingExpression path error: 'IsInitialStep' property not found on 'object' ''SFCBranchViewModel' (HashCode=42949880)'. BindingExpression:Path=IsInitialStep; DataItem='SFCBranchViewModel' (HashCode=42949880); target element is 'SFCConnectPoint' (Name='RightParallelPoint'); target property is 'NoTarget' (type 'Object')
[SFCParallelBranchView] 开始设置连接点ElementId: 7a774e43-90a9-4ce4-9fd4-80b96dc0d06e
[SFCParallelBranchView] ✅ LeftTop连接点ElementId设置: 7a774e43-90a9-4ce4-9fd4-80b96dc0d06e
[SFCParallelBranchView] ✅ LeftParallel连接点ElementId设置: 7a774e43-90a9-4ce4-9fd4-80b96dc0d06e
[SFCParallelBranchView] ✅ RightParallel连接点ElementId设置: 7a774e43-90a9-4ce4-9fd4-80b96dc0d06e
[SFCParallelBranchView] ✅ 已绑定LeftTop连接点适配器: 7a774e43-90a9-4ce4-9fd4-80b96dc0d06e
[SFCParallelBranchView] ✅ 已绑定LeftParallel连接点适配器: 7a774e43-90a9-4ce4-9fd4-80b96dc0d06e
[SFCParallelBranchView] ✅ 已绑定RightParallel连接点适配器: 7a774e43-90a9-4ce4-9fd4-80b96dc0d06e
[SFCParallelBranchView] 开始同步现有连接状态: 7a774e43-90a9-4ce4-9fd4-80b96dc0d06e
[SFCParallelBranchView] 找到 2 个相关连接
[SFCParallelBranchView] ✅ 同步输出连接: fbf2d79e-0d84-4288-a3fb-2eda35e3cd8c, 索引: 2
System.Windows.Data Error: 4 : Cannot find source for binding with reference 'RelativeSource FindAncestor, AncestorType='System.Windows.Controls.UserControl', AncestorLevel='1''. BindingExpression:Path=DataContext.InsertSelectionBranchFromAnyPartCommand; DataItem=null; target element is 'MenuItem' (Name=''); target property is 'Command' (type 'ICommand')
System.Windows.Data Error: 4 : Cannot find source for binding with reference 'RelativeSource FindAncestor, AncestorType='System.Windows.Controls.UserControl', AncestorLevel='1''. BindingExpression:Path=DataContext.InsertSiblingBranchCommand; DataItem=null; target element is 'MenuItem' (Name=''); target property is 'Command' (type 'ICommand')
System.Windows.Data Error: 4 : Cannot find source for binding with reference 'RelativeSource FindAncestor, AncestorType='System.Windows.Controls.UserControl', AncestorLevel='1''. BindingExpression:Path=DataContext.InsertParallelBranchFromAnyPartCommand; DataItem=null; target element is 'MenuItem' (Name=''); target property is 'Command' (type 'ICommand')
[IsParallelBranchConnection] 并行分支间连接: 193f1508-b89e-4086-834a-6bbf63f93f2e -> 7a774e43-90a9-4ce4-9fd4-80b96dc0d06e, 距离: 1.5px
连接 98031ff4-a506-42ea-863f-1a1eb39eeade 连接点重叠，使用连接点重叠方式
[AddConnection] 延迟更新连接线 98031ff4-a506-42ea-863f-1a1eb39eeade 的路径点
[CalculateElementConnectPoint] 🔴 并行分支右侧连接点(索引0): 387.13636363636414,472.74545454545506
[CalculateElementConnectPoint] 🔵 并行分支左侧连接点(索引1): 388.63636363636414,472.74545454545506
[AddConnection] 延迟更新 - 源对象类型: SFCBranchViewModel, 目标对象类型: SFCBranchViewModel
[AddConnection] 延迟更新 - 源位置: 199.13636363636414,448.24545454545506, 目标位置: 346.63636363636414,448.24545454545506
[AddConnection] 延迟更新后的路径点: 387.13636363636414,472.74545454545506 -> 388.63636363636414,472.74545454545506
[AddConnection] PathPoints集合已更新，应该触发重新评估
[连接点重叠检测] 点1: (534.6, 472.7), 点2: (534.6, 468.2), 距离: 4.5px, 重叠: True
[CreateConnectionPath] 连接 fbf2d79e-0d84-4288-a3fb-2eda35e3cd8c 连接点重叠，隐藏连接线
[CreateConnectionPath] 起点: (534.6, 472.7), 终点: (534.6, 468.2)
连接 fbf2d79e-0d84-4288-a3fb-2eda35e3cd8c 连接点重叠，使用连接点重叠方式
[AddConnection] 延迟更新连接线 fbf2d79e-0d84-4288-a3fb-2eda35e3cd8c 的路径点
[AddConnection] 延迟更新 - 源对象类型: SFCBranchViewModel, 目标对象类型: SFCStepViewModel
[AddConnection] 延迟更新 - 源位置: 346.63636363636414,448.24545454545506, 目标位置: 484.63636363636414,462.24545454545506
[AddConnection] 延迟更新后的路径点: 534.6363636363642,472.74545454545506 -> 534.6363636363642,468.24545454545506
[AddConnection] PathPoints集合已更新，应该触发重新评估
