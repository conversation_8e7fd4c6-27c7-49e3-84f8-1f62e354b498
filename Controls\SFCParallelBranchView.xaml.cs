using System;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using PC_Control2.Demo.Models;
using PC_Control2.Demo.ViewModels;

namespace PC_Control2.Demo.Controls
{
    /// <summary>
    /// SFCParallelBranchView.xaml 的交互逻辑
    /// </summary>
    public partial class SFCParallelBranchView : UserControl
    {
        #region 私有字段

        private SFCConnectPoint? _leftTopConnectPoint;
        private SFCConnectPoint? _leftParallelPoint;
        private SFCConnectPoint? _rightParallelPoint;
        private SFCBranchViewModel? _viewModel;

        // 拖拽相关
        private bool _isDragging = false;
        private Point _dragStartPoint;

        #endregion

        #region 属性

        /// <summary>
        /// ViewModel属性
        /// </summary>
        public SFCBranchViewModel? ViewModel => _viewModel;

        #endregion

        #region 依赖属性
        
        /// <summary>
        /// 连接交互管理器依赖属性
        /// </summary>
        public static readonly DependencyProperty InteractionManagerProperty =
            DependencyProperty.Register("InteractionManager", typeof(SFCConnectPointInteractionManager), typeof(SFCParallelBranchView),
                new PropertyMetadata(null, OnInteractionManagerChanged));
                
        #endregion
        
        #region 公共属性
        
        /// <summary>
        /// 连接交互管理器
        /// </summary>
        public SFCConnectPointInteractionManager? InteractionManager
        {
            get { return (SFCConnectPointInteractionManager?)GetValue(InteractionManagerProperty); }
            set { SetValue(InteractionManagerProperty, value); }
        }
        
        #endregion
        
        #region 构造函数
        
        public SFCParallelBranchView()
        {
            InitializeComponent();

            // 数据上下文变化事件
            DataContextChanged += OnDataContextChanged;

            // 控件加载完成事件
            Loaded += OnLoaded;

            // 设置可拖拽
            this.MouseLeftButtonDown += OnMouseLeftButtonDown;
            this.MouseLeftButtonUp += OnMouseLeftButtonUp;
            this.MouseMove += OnMouseMove;
        }
        
        #endregion
        
        #region 事件处理
        
        /// <summary>
        /// 数据上下文变化处理
        /// </summary>
        private void OnDataContextChanged(object sender, DependencyPropertyChangedEventArgs e)
        {
            _viewModel = e.NewValue as SFCBranchViewModel;

            // 🔧 关键修复：只有并行分支才初始化连接点
            if (_viewModel != null && _viewModel.BranchType == SFCBranchType.Parallel)
            {
                InitializeConnectPoints();
            }
        }
        
        /// <summary>
        /// 控件加载完成处理
        /// </summary>
        private void OnLoaded(object sender, RoutedEventArgs e)
        {
            // 🔧 关键修复：只有并行分支才初始化连接点
            if (_viewModel != null && _viewModel.BranchType == SFCBranchType.Parallel)
            {
                InitializeConnectPoints();
            }
        }
        
        /// <summary>
        /// 交互管理器变化处理
        /// </summary>
        private static void OnInteractionManagerChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is SFCParallelBranchView branchView)
            {
                branchView.UpdateConnectPointsInteractionManager();
            }
        }
        
        #endregion
        
        #region 私有方法
        
        /// <summary>
        /// 初始化连接点
        /// </summary>
        private void InitializeConnectPoints()
        {
            if (_viewModel == null)
                return;

            // 查找连接点控件
            _leftTopConnectPoint = LeftTopConnectPoint as SFCConnectPoint;
            _leftParallelPoint = LeftParallelPoint as SFCConnectPoint;
            _rightParallelPoint = RightParallelPoint as SFCConnectPoint;

            // 🔧 关键修复：优先设置ElementId，确保在适配器绑定之前ElementId正确
            SetConnectPointElementIds();
            
            // 如果连接点控件不存在，则创建
            if (_leftTopConnectPoint == null)
            {
                _leftTopConnectPoint = new SFCConnectPoint
                {
                    ElementId = _viewModel.Id,
                    ElementType = SFCElementType.ParallelBranch,
                    PointType = ConnectPointType.Input,
                    Index = 0
                };
                
                // 替换原有连接点
                if (LeftTopConnectPoint != null)
                {
                    int index = MainCanvas.Children.IndexOf(LeftTopConnectPoint);
                    if (index >= 0)
                    {
                        MainCanvas.Children[index] = _leftTopConnectPoint;
                        Canvas.SetLeft(_leftTopConnectPoint, Canvas.GetLeft(LeftTopConnectPoint));
                        Canvas.SetTop(_leftTopConnectPoint, Canvas.GetTop(LeftTopConnectPoint));
                    }
                }
            }
            
            if (_leftParallelPoint == null)
            {
                _leftParallelPoint = new SFCConnectPoint
                {
                    ElementId = _viewModel.Id,
                    ElementType = SFCElementType.ParallelBranch,
                    PointType = ConnectPointType.Output,  // 🔧 关键修复：左侧并行连接点应该是输出类型
                    Index = 1
                };
                
                // 替换原有连接点
                if (LeftParallelPoint != null)
                {
                    int index = MainCanvas.Children.IndexOf(LeftParallelPoint);
                    if (index >= 0)
                    {
                        MainCanvas.Children[index] = _leftParallelPoint;
                        Canvas.SetLeft(_leftParallelPoint, Canvas.GetLeft(LeftParallelPoint));
                        Canvas.SetTop(_leftParallelPoint, Canvas.GetTop(LeftParallelPoint));
                    }
                }
            }
            
            if (_rightParallelPoint == null)
            {
                _rightParallelPoint = new SFCConnectPoint
                {
                    ElementId = _viewModel.Id,
                    ElementType = SFCElementType.ParallelBranch,
                    PointType = ConnectPointType.Output,
                    Index = 2  // 🔧 修复：避免与LeftTop的Index=0冲突
                };
                
                // 替换原有连接点
                if (RightParallelPoint != null)
                {
                    int index = MainCanvas.Children.IndexOf(RightParallelPoint);
                    if (index >= 0)
                    {
                        MainCanvas.Children[index] = _rightParallelPoint;
                        Canvas.SetLeft(_rightParallelPoint, Canvas.GetLeft(RightParallelPoint));
                        Canvas.SetTop(_rightParallelPoint, Canvas.GetTop(RightParallelPoint));
                    }
                }
            }
            
            // 🔧 关键修复：设置连接点适配器，确保连接状态正确显示
            if (_viewModel.ConnectPointAdapters != null && _viewModel.ConnectPointAdapters.Count >= 3)
            {
                if (_leftTopConnectPoint != null)
                {
                    _leftTopConnectPoint.Adapter = _viewModel.ConnectPointAdapters[0];
                    System.Diagnostics.Debug.WriteLine($"[SFCParallelBranchView] ✅ 已绑定LeftTop连接点适配器: {_viewModel.Id}");
                }

                if (_leftParallelPoint != null)
                {
                    _leftParallelPoint.Adapter = _viewModel.ConnectPointAdapters[1];
                    System.Diagnostics.Debug.WriteLine($"[SFCParallelBranchView] ✅ 已绑定LeftParallel连接点适配器: {_viewModel.Id}");
                }

                if (_rightParallelPoint != null)
                {
                    _rightParallelPoint.Adapter = _viewModel.ConnectPointAdapters[2];
                    System.Diagnostics.Debug.WriteLine($"[SFCParallelBranchView] ✅ 已绑定RightParallel连接点适配器: {_viewModel.Id}");
                }

                // 🔧 关键修复：适配器绑定完成后，同步现有连接状态
                SyncExistingConnectionStates();
            }
            else
            {
                System.Diagnostics.Debug.WriteLine($"[SFCParallelBranchView] ❌ 警告：并行分支 {_viewModel.Id} 缺少连接点适配器，期望3个，实际{_viewModel.ConnectPointAdapters?.Count ?? 0}个");
            }
            
            // 更新交互管理器
            UpdateConnectPointsInteractionManager();
        }

        /// <summary>
        /// 设置连接点的ElementId，确保在适配器绑定之前正确设置
        /// </summary>
        private void SetConnectPointElementIds()
        {
            if (_viewModel == null) return;

            System.Diagnostics.Debug.WriteLine($"[SFCParallelBranchView] 开始设置连接点ElementId: {_viewModel.Id}");

            if (_leftTopConnectPoint != null)
            {
                _leftTopConnectPoint.ElementId = _viewModel.Id;
                _leftTopConnectPoint.ElementType = SFCElementType.ParallelBranch;
                _leftTopConnectPoint.PointType = ConnectPointType.Input;
                _leftTopConnectPoint.Index = 0;
                System.Diagnostics.Debug.WriteLine($"[SFCParallelBranchView] ✅ LeftTop连接点ElementId设置: {_leftTopConnectPoint.ElementId}");
            }

            if (_leftParallelPoint != null)
            {
                _leftParallelPoint.ElementId = _viewModel.Id;
                _leftParallelPoint.ElementType = SFCElementType.ParallelBranch;
                _leftParallelPoint.PointType = ConnectPointType.Input;
                _leftParallelPoint.Index = 1;
                System.Diagnostics.Debug.WriteLine($"[SFCParallelBranchView] ✅ LeftParallel连接点ElementId设置: {_leftParallelPoint.ElementId}");
            }

            if (_rightParallelPoint != null)
            {
                _rightParallelPoint.ElementId = _viewModel.Id;
                _rightParallelPoint.ElementType = SFCElementType.ParallelBranch;
                _rightParallelPoint.PointType = ConnectPointType.Output;
                _rightParallelPoint.Index = 2;
                System.Diagnostics.Debug.WriteLine($"[SFCParallelBranchView] ✅ RightParallel连接点ElementId设置: {_rightParallelPoint.ElementId}");
            }
        }

        /// <summary>
        /// 同步现有连接状态到适配器
        /// </summary>
        private void SyncExistingConnectionStates()
        {
            if (_viewModel == null) return;

            System.Diagnostics.Debug.WriteLine($"[SFCParallelBranchView] 开始同步现有连接状态: {_viewModel.Id}");

            // 获取SFC主ViewModel来查找现有连接
            var mainViewModel = FindMainSFCViewModel();
            if (mainViewModel == null)
            {
                System.Diagnostics.Debug.WriteLine($"[SFCParallelBranchView] ❌ 无法找到主SFC ViewModel");
                return;
            }

            // 查找与此元素相关的所有连接
            var relatedConnections = mainViewModel.Connections
                .Where(c => c.SourceId == _viewModel.Id || c.TargetId == _viewModel.Id)
                .ToList();

            System.Diagnostics.Debug.WriteLine($"[SFCParallelBranchView] 找到 {relatedConnections.Count} 个相关连接");

            foreach (var connection in relatedConnections)
            {
                if (connection.SourceId == _viewModel.Id)
                {
                    // 这是输出连接，更新输出适配器
                    var outputAdapter = _viewModel.ConnectPointAdapters
                        .FirstOrDefault(a => a.Direction == ConnectPointDirection.Output && a.ConnectPointIndex == connection.SourceConnectPointIndex);
                    if (outputAdapter != null)
                    {
                        outputAdapter.AddConnection(connection.Id);
                        System.Diagnostics.Debug.WriteLine($"[SFCParallelBranchView] ✅ 同步输出连接: {connection.Id}, 索引: {connection.SourceConnectPointIndex}");
                    }
                }

                if (connection.TargetId == _viewModel.Id)
                {
                    // 这是输入连接，更新输入适配器
                    var inputAdapter = _viewModel.ConnectPointAdapters
                        .FirstOrDefault(a => a.Direction == ConnectPointDirection.Input && a.ConnectPointIndex == connection.TargetConnectPointIndex);
                    if (inputAdapter != null)
                    {
                        inputAdapter.AddConnection(connection.Id);
                        System.Diagnostics.Debug.WriteLine($"[SFCParallelBranchView] ✅ 同步输入连接: {connection.Id}, 索引: {connection.TargetConnectPointIndex}");
                    }
                }
            }
        }

        /// <summary>
        /// 查找主SFC ViewModel
        /// </summary>
        private EnhancedSFCViewModel? FindMainSFCViewModel()
        {
            var current = this.Parent;
            while (current != null)
            {
                if (current is FrameworkElement element && element.DataContext is EnhancedSFCViewModel sfcViewModel)
                {
                    return sfcViewModel;
                }
                current = LogicalTreeHelper.GetParent(current);
            }
            return null;
        }
        
        /// <summary>
        /// 更新连接点的交互管理器
        /// </summary>
        private void UpdateConnectPointsInteractionManager()
        {
            if (_leftTopConnectPoint != null)
            {
                _leftTopConnectPoint.InteractionManager = InteractionManager;
            }
            
            if (_leftParallelPoint != null)
            {
                _leftParallelPoint.InteractionManager = InteractionManager;
            }
            
            if (_rightParallelPoint != null)
            {
                _rightParallelPoint.InteractionManager = InteractionManager;
            }
        }

        #endregion

        #region 拖拽功能

        private void OnMouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (ViewModel == null) return;

            // 选中并行分支
            ViewModel.SelectCommand?.Execute(null);

            // 开始拖拽
            _isDragging = true;
            _dragStartPoint = e.GetPosition(this);
            this.CaptureMouse();

            // 不设置e.Handled = true，让事件继续冒泡到SFCCanvas
        }

        private void OnMouseLeftButtonUp(object sender, MouseButtonEventArgs e)
        {
            if (_isDragging)
            {
                _isDragging = false;
                this.ReleaseMouseCapture();
                e.Handled = true;
            }
        }

        private void OnMouseMove(object sender, MouseEventArgs e)
        {
            if (_isDragging && ViewModel != null && e.LeftButton == MouseButtonState.Pressed)
            {
                var currentPosition = e.GetPosition(this);
                var deltaX = currentPosition.X - _dragStartPoint.X;
                var deltaY = currentPosition.Y - _dragStartPoint.Y;

                // 更新并行分支位置
                var newPosition = new Point(
                    ViewModel.Position.X + deltaX,
                    ViewModel.Position.Y + deltaY);

                System.Diagnostics.Debug.WriteLine($"[SFCParallelBranchView] 拖动更新位置: {ViewModel.Id} -> {newPosition}");
                ViewModel.Position = newPosition;
                e.Handled = true;
            }
        }

        #endregion
    }
}
