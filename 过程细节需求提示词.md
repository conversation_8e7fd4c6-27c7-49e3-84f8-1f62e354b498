## 关于SFC顺控器元素的连接线

连接线设计意图：
1.当选中现有的元素插入另一个新的元素时；默认把新元素的输入连接点和现有元素的输出连接点进行重叠；
2.但是即使重叠了，后台应该也是有连接线连接的，只是由于两个连接点重叠，被盖住了，所以看不到连接线；如果我把元素移开，应该能看到两个连接点之间的连接线。
3.连接线，也请使用和蓝图编辑器中，一样的贝塞尔曲线这种方式；
4.若两个连接点之间已经完全没有重叠的部分了，则需要显示连接线；并且连接线的两端一定是落在对应的连接点上，不能悬空；
5.若两个连接点有重叠部分，只要有一点重叠，则隐藏连接线；

## 预期效果

测试步骤
1. 基本连接测试
启动程序，打开SFC编辑器
添加步骤S1：在画布上点击添加一个步骤
选中S1，然后插入转换条件T1
选中T1，然后插入步骤S2
2. 连接线显示测试
观察以下几个关键点：

连接点位置对齐：

S1的下连接点（绿色圆点）和T1的上连接点（白色圆点）应该完全重叠
T1的下连接点（绿色圆点）和S2的上连接点（白色圆点）应该完全重叠
连接线终点连接：

从S1下连接点出发的贝塞尔曲线应该精确连接到T1的上连接点，不再悬空；此时因为两个连接点完全重叠，所以看不到连接线；
从T1下连接点出发的贝塞尔曲线应该精确连接到S2的上连接点，不再悬空；此时因为两个连接点完全重叠，所以看不到连接线；
3. 元素移动测试
拖动T1元素，将其稍微移开S1和S2
观察连接线：
应该能看到从S1到T1的完整贝塞尔曲线连接线
应该能看到从T1到S2的完整贝塞尔曲线连接线
连接线的起点和终点都应该准确连接到对应的连接点上
4. 连接点重叠测试
将T1拖回原位置，使连接点重新重叠
观察连接线：当连接点距离很近时（小于10像素），连接线应该自动隐藏，显示连接点重叠效果
测试成功的标志：

✅ 连接线不再悬空，精确连接到目标连接点
✅ 连接点位置完全重叠（绿色+白色圆点重合）
✅ 移动元素后连接线正确显示和更新
✅ 连接点重叠时连接线自动隐藏


### SFC小功能完善
还有几个待完善的小任务，这些小任务算是之前遗留下来的，请你帮我规划到@d:\桌面\PC_Control2\PC_Ctrl/SFC核心功能实施计划_优化版.md中：
1.目前版本的SFC编辑器中，只有步骤、转换条件、选择分支、并行分支、顺控器终止，这几个顺控器元素；基于IEC61131，还缺少一个跳转元素；需要增加跳转这个元素，并且其它元素有的功能，这个元素也要有，比如基本的选中、拖拽、鼠标悬停等交互功能、右键菜单功能、连接线功能等等，其它我没提及的，不代表没有，需要参考其它元素的功能。
2.目前SFC编辑器中，在画布中插入某个顺控器元素，元素的插入位置都是不正确的；正确的插入位置，应该是新插入元素的输入连接点，要和选中元素的输出连接点，坐标完全重合；视觉体验上，两根元素的连接点是完全重叠的；
3.分支大小和布局动态调整功能的完善：目前的选择分支和并行分支，都是固定死的长度和大小；而在实际使用时，如果在某个分支下面，嵌套另一个分支，此时主分支的长度大小如果不动态变化，就会造成元素的重叠，此时需要动态调整分支的大小和空间布局。

关于分支大小和布局动态调整的详细示例解释：
如图，这是西门子博图中Graph的示例：
- 蓝色框内，表示选择分支的正常大小长度；
- 绿色框内，表示的是在主分支T50下方，嵌套插入了三个子分支：T62、T59、T60；
- 红色框内，表示的是，由于嵌套了三个子分支，造成原先的主分支T51的长度变长了；

### 跳转元素设计
请参考西门子Graph中的交互体验：
1、样式外观：
- 最上方一个连接点：参考其它元素的圆形连接点设计，直接复用XAML样式代码即可；
- 连接点下方有一条垂直的线段：顺控器终止这个元素中，也有一条垂直线段，直接复用这个垂直线段的XAML样式代码即可；
- 垂直线段下方有一个箭头，类似示例图片中的箭头；
- 箭头右侧有一个跳转的步号标签，需要显示出来

2、交互设计：
- 点击插入跳转元素后，会插入跳转元素到画布中；
- 紧接着，自动弹出待跳转步骤步号的列表窗口，可以参考下面的示例图片中的列表功能；
- 选中列表中的某一个步骤号，就确认了跳转的目标步骤；（比如选择了步骤S2）
- 在选中步骤的上方，就会出现一个跳转箭头，如下图，绿色框内：
- 已经选好的步骤号，双击鼠标可以进行更改步骤号；更改之后，箭头位置也会更新到新的步骤上方；




